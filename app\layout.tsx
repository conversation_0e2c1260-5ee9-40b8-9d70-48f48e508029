import type { <PERSON>ada<PERSON> } from "next";
import { GeistSans } from "geist/font/sans";
import { GeistMono } from "geist/font/mono";
import { Analytics } from "@vercel/analytics/next";
import "./globals.css";
import Micht<PERSON><PERSON><PERSON><PERSON> from "./icon.jpg";

const frontendUrl = process.env.FRONTEND_URL || ''

export const metadata: Metadata = {
  title: "Developer Portfolio • Michthebrand",
  description: "Fullstack | Blockchain Developer Portfolio",
  metadataBase: new URL(frontendUrl),
  openGraph: {
    title: "Developer Portfolio • Michthebrand",
    description: "Fullstack | Blockchain Developer Portfolio",
    images: [
      {
        url: MichthebrandLogo.src,
      },
    ],
    url: frontendUrl,
    type: "website",
    siteName: "Developer Portfolio  • Michthebrand",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Developer Portfolio  • Michthebrand",
    description: "Fullstack | Blockchain Developer Portfolio",
    images: [{ url: MichthebrandLogo.src }],
  },
  icons: {
    icon: MichthebrandLogo.src,
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html className="no-scrollbar" lang="en">
      <head>
        <style>{`
html {
  font-family: ${GeistSans.style.fontFamily};
  --font-sans: ${GeistSans.variable};
  --font-mono: ${GeistMono.variable};
}
        `}</style>
      </head>
      <body className="min-h-screen overflow-y-auto no-scrollbar">
        {children}
        <Analytics />
      </body>
    </html>
  );
}
