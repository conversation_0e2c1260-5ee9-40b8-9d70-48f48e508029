// react component with imports
export const Twitter = (props: React.JSX.IntrinsicElements["svg"]) => {
  return (
    <svg
      fill="#383D38"
      {...props}
      width="64"
      height="60"
      viewBox="0 0 64 60"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.153931 0L24.8634 33.0459L0 59.9144H5.59739L27.3674 36.3918L44.9554 59.9144H64L37.9014 25.0097L61.0455 0H55.4481L35.4012 21.6639L19.2023 0H0.157643H0.153931ZM8.3829 4.12331H17.13L55.7635 55.7912H47.0164L8.3829 4.12331Z"
        fill="inherit"
      />
    </svg>
  );
};

export const Whatsapp = (props: React.JSX.IntrinsicElements["svg"]) => {
  return (
    <svg
      fill="#383D38"
      {...props}
      width="67"
      height="64"
      viewBox="0 0 67 64"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M43.2162 46.8621C30.22 46.8621 19.6466 36.282 19.6431 23.282C19.6466 19.9866 22.3291 17.3069 25.6164 17.3069C25.9544 17.3069 26.2888 17.3354 26.609 17.3923C27.3134 17.5097 27.9823 17.7482 28.5977 18.1076C28.6867 18.161 28.7472 18.2464 28.7614 18.3461L30.1346 27.0044C30.1524 27.1076 30.1204 27.2073 30.0528 27.282C29.295 28.1218 28.3273 28.7269 27.2494 29.0294L26.73 29.1753L26.9256 29.677C28.6973 34.1895 32.3048 37.7945 36.8195 39.5738L37.3212 39.7731L37.467 39.2536C37.7694 38.1753 38.3742 37.2073 39.2138 36.4493C39.2743 36.3923 39.3561 36.3639 39.438 36.3639C39.4558 36.3639 39.4736 36.3639 39.4949 36.3674L48.1507 37.7411C48.2539 37.7589 48.3393 37.8158 48.3926 37.9048C48.7484 38.5205 48.9868 39.193 49.1077 39.8976C49.1647 40.2108 49.1896 40.5418 49.1896 40.887C49.1896 44.1788 46.5106 46.8585 43.2162 46.8621Z"
        fill="inherit"
      />
      <path
        d="M66.2626 29.1537C65.5617 21.232 61.9329 13.8833 56.045 8.46334C50.1214 3.01138 42.4368 0.0078125 34.4001 0.0078125C16.7611 0.0078125 2.40947 14.3637 2.40947 32.0078C2.40947 37.9295 4.04241 43.6982 7.13403 48.7231L0.239258 63.9901L22.3146 61.6377C26.1534 63.2107 30.2162 64.0078 34.3965 64.0078C35.4958 64.0078 36.6236 63.9509 37.755 63.8334C38.7511 63.7267 39.7579 63.5702 40.747 63.3709C55.5184 60.3851 66.3017 47.2712 66.3871 32.1786V32.0078C66.3871 31.047 66.3444 30.0861 66.259 29.1573L66.2626 29.1537ZM23.1649 54.9366L10.9515 56.2392L14.5981 48.1573L13.8687 47.1787C13.8154 47.1075 13.762 47.0363 13.7015 46.9544C10.5352 42.5808 8.86306 37.4135 8.86306 32.0114C8.86306 17.926 20.3188 6.46686 34.4001 6.46686C47.5919 6.46686 58.7737 16.7623 59.8517 29.9046C59.9086 30.6093 59.9406 31.3174 59.9406 32.0149C59.9406 32.2142 59.937 32.4099 59.9335 32.6199C59.6631 44.4028 51.4342 54.41 39.9216 56.958C39.0428 57.1538 38.1427 57.3032 37.2462 57.3993C36.3141 57.5061 35.3571 57.5595 34.4072 57.5595C31.0238 57.5595 27.7365 56.9047 24.6307 55.6093C24.2856 55.4705 23.9476 55.321 23.631 55.168L23.1685 54.9438L23.1649 54.9366Z"
        fill="inherit"
      />
    </svg>
  );
};

export const Linkedin = (props: React.JSX.IntrinsicElements["svg"]) => {
  return (
    <svg
      fill="#383D38"
      {...props}
      width="64"
      height="60"
      viewBox="0 0 64 64"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.2441 12.6659C0.745345 11.2554 0 9.50952 0 7.43223C0 5.35494 0.749331 3.5322 2.2441 2.1177C3.74286 0.707245 5.67212 0 8.03585 0C10.3996 0 12.2531 0.707245 13.7479 2.1177C15.2467 3.52816 15.992 5.3024 15.992 7.43223C15.992 9.56206 15.2427 11.2554 13.7479 12.6659C12.2491 14.0763 10.3478 14.7836 8.03585 14.7836C5.72394 14.7836 3.74286 14.0763 2.2441 12.6659ZM14.7324 20.7568V64H1.25557V20.7568H14.7324Z"
        fill="inherit"
      />
      <path
        d="M59.5955 25.0285C62.5332 28.2617 64.0001 32.6992 64.0001 38.3491V63.2362H51.2009V40.1031C51.2009 37.2539 50.4715 35.0392 49.0166 33.463C47.5617 31.8869 45.6005 31.0947 43.1451 31.0947C40.6897 31.0947 38.7285 31.8828 37.2736 33.463C35.8187 35.0392 35.0892 37.2539 35.0892 40.1031V63.2362H22.2144V20.6356H35.0892V26.2855C36.3927 24.4022 38.1506 22.9149 40.3589 21.8197C42.5672 20.7244 45.0504 20.1788 47.8128 20.1788C52.7316 20.1788 56.6617 21.7954 59.5955 25.0285Z"
        fill="inherit"
      />
    </svg>
  );
};
