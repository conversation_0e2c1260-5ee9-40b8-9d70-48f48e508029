"use client";
import { useState } from "react";
import { projects } from "./_projects";
import { AnimatePresence, motion } from "motion/react";
import { Twitter } from "./icons/social-media";
import PuzzlePieceProject from "../_projects/puzzle-piece";

const Projects = () => {
  const [currentWatching, setCurrentWatching] = useState(0);
  const currentProject = projects[currentWatching];
  return (
    <>
      {/* Main Content Container */}
      <div className="relative w-full h-screen flex items-center justify-center">
        {/* Large Background Circles */}
        <div className="absolute left-0 top-1/2 -translate-y-1/2 w-96 h-96 rounded-full bg-gray-200/50 -translate-x-1/2"></div>
        <div className="absolute right-0 top-1/2 -translate-y-1/2 w-80 h-80 rounded-full border border-dashed border-gray-400 -translate-x-1/4"></div>

        {/* Vertical DESIGN Text */}
        <div className="absolute left-16 top-1/2 -translate-y-1/2 -rotate-90 origin-center hidden min-[480px]:block">
          <h1 className="text-6xl font-bold text-gray-800 tracking-wider capitalize">
            {currentProject.title.toUpperCase()}
          </h1>
        </div>

        {/* Central Geometric Object */}
        <PuzzlePieceProject />

        {/* Dotted Pattern Elements */}
        <div className="absolute left-1/3 top-1/3 grid grid-cols-8 gap-1">
          {Array.from({ length: 64 }).map((_, i) => (
            <div key={i} className="w-1 h-1 bg-gray-400 rounded-full"></div>
          ))}
        </div>

        {/* Decorative X Symbols */}
        <div className="absolute top-16 left-1/2 -translate-x-1/2 text-gray-600 text-xl">
          ✕✕
        </div>
        <div className="absolute bottom-20 left-1/2 -translate-x-1/2 text-gray-600 text-xl">
          ✕✕
        </div>

        {/* Meteorite Label */}
        <div className="absolute right-16 top-1/2 -translate-y-1/2">
          <div className="border border-gray-600 px-4 py-8 rotate-90">
            <span className="text-gray-600 text-xs font-medium tracking-widest">
              {currentProject.projectType.toUpperCase()}
            </span>
          </div>
        </div>

        {/* Dotted Lines */}
        <div className="absolute top-1/4 right-1/4 w-32 h-px border-t border-dashed border-gray-400 rotate-45"></div>
        <div className="absolute bottom-1/3 left-1/3 w-24 h-px border-t border-dashed border-gray-400 -rotate-12"></div>
      </div>

      {/* Bottom Left Text */}
      <AnimatePresence mode="wait">
        {currentWatching === 0 && (
          <motion.div
            initial="hide"
            animate="shown"
            exit="hide"
            key={"name_title"}
            className="absolute bottom-16 left-8"
          >
            <motion.div className="space-y-2">
              <motion.h2 className="text-gray-800 text-lg font-medium overflow-hidden tracking-wider">
                <motion.span
                  variants={{
                    hide: { y: 100, transition: { delay: 0.1 } },
                    shown: { y: 0 },
                  }}
                  transition={{
                    type: "keyframes",
                  }}
                  className="inline-block"
                >
                  CHARLES IKECHUKWU
                </motion.span>
              </motion.h2>
              <motion.p className="text-gray-600 text-sm overflow-hidden tracking-wide">
                <motion.span
                  variants={{
                    hide: { y: 100, transition: { delay: 0.2 } },
                    shown: { y: 0 },
                  }}
                  transition={{
                    type: "keyframes",
                  }}
                  className="inline-block"
                >
                  FULL-STACK DEVELOPER
                </motion.span>
              </motion.p>
              {/*show link title in text-sm here*/}
              <motion.div className="overflow-hidden">
                <motion.div
                  variants={{
                    hide: { y: -100, transition: { delay: 0.1 } },
                    shown: { y: 0 },
                  }}
                  transition={{
                    type: "keyframes",
                  }}
                  className="flex py-0.5 gap-x-4"
                >
                  <a href="https://x.com/mich_thedev" target="_blank">
                    <h3 className="text-xl font-medium text-gray-800 tracking-wide mb-2">
                      <Twitter className="size-[22px] " />
                    </h3>
                  </a>
                  <a
                    href="https://github.com/michthebrandofficial"
                    target="_blank"
                    className="inline-block -mt-0.5"
                  >
                    <img
                      src={
                        "https://banner2.cleanpng.com/20180502/xgw/kisspng-github-computer-icons-directory-ming-hua-dado-5aea7d1bc225d6.5661248715253168917952.jpg"
                      }
                      className="size-[26px] bg-transparent "
                    />
                  </a>
                </motion.div>
              </motion.div>
            </motion.div>
          </motion.div>
        )}

        {!(currentWatching === 0) && (
          <motion.div
            key={"previous"}
            whileHover={{ x: -10 }}
            initial={false}
            animate={{
              transition: {
                delay: 0.6,
              },
            }}
            onTap={() => {
              if (currentWatching === 0) return;
              else setCurrentWatching((p) => p - 1);
            }}
            className="absolute bottom-16 left-8 cursor-pointer"
          >
            <div className="flex items-center gap-2">
              <motion.span
                initial={{ x: -10 }}
                animate={{ x: 0 }}
                className="text-gray-600 text-lg"
              >
                ←
              </motion.span>
              <span className="text-gray-600 text-sm font-medium tracking-wider">
                PREVIOUS
              </span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      {/* Bottom Right Navigation */}
      {!(currentWatching === projects.length - 1) && (
        <motion.div
          key={"next"}
          whileHover={{ x: 10 }}
          transition={{ delay: -0.6 }}
          onTap={() => {
            if (currentWatching === projects.length - 1) return;
            else setCurrentWatching((p) => p + 1);
          }}
          className="absolute bottom-16 right-8 cursor-pointer"
        >
          <div className="flex items-center gap-2">
            <span className="text-gray-600 text-sm font-medium tracking-wider">
              NEXT
            </span>
            <motion.span
              initial={{ x: 10 }}
              animate={{ x: 0 }}
              className="text-gray-600 text-lg"
            >
              →
            </motion.span>
          </div>
        </motion.div>
      )}

      {/* Side Decorative Elements */}
      <div className="absolute left-8 top-1/2 -translate-y-1/2 flex flex-col gap-2">
        <div className="w-px h-4 bg-gray-400"></div>
        <div className="w-px h-4 bg-gray-400"></div>
        <div className="w-px h-4 bg-gray-400"></div>
      </div>
    </>
  );
};

export default Projects;
