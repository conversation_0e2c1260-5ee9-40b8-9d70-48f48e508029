type StringWithNewLine = string;

export type ProjectType = {
  title: string;
  /**
   * @dev tags are the things that will fill up the `XX` in the design
   */
  tags: string[],
  projectType: string;
  description: string;
  link: string | null;
  github: string | null
  deep_dive: StringWithNewLine
} & (
  {
    type: 'video',
    videoUrl: string
  } | {
    type: 'image',
    imageUrl: string
  }
)

export const projects: ProjectType[] = [
  {
    title: 'NixixJS',
    tags: ['tag1', 'tag2'],
    description: 'Reactivity Library using TypeScript',
    type: 'video',
    // generate long text explaining the project
    deep_dive: 'NixixJS is a reactivity library that uses TypeScript for type safety and performance. It provides a simple and intuitive API for creating reactive components and managing state. \n\nThe library is designed to be easy to use and integrate with existing projects, making it a great choice for developers who want to build scalable and maintainable applications.',
    link: null,
    projectType: 'Library',
    github: 'https://github.com/nixixjs/nixix',
    videoUrl: 'https://example.com/video1.mp4'
  },
  {
    title: 'NixixJS',
    tags: ['tag1', 'tag2'],
    description: 'Reactivity Library using TypeScript',
    type: 'video',
    // generate long text explaining the project
    deep_dive: 'NixixJS is a reactivity library that uses TypeScript for type safety and performance. It provides a simple and intuitive API for creating reactive components and managing state. \n\nThe library is designed to be easy to use and integrate with existing projects, making it a great choice for developers who want to build scalable and maintainable applications.',
    link: null,
    projectType: 'Library',
    github: 'https://github.com/nixixjs/nixix',
    videoUrl: 'https://example.com/video1.mp4'
  },
  {
    title: 'NixixJS',
    tags: ['tag1', 'tag2'],
    description: 'Reactivity Library using TypeScript',
    type: 'video',
    // generate long text explaining the project
    deep_dive: 'NixixJS is a reactivity library that uses TypeScript for type safety and performance. It provides a simple and intuitive API for creating reactive components and managing state. \n\nThe library is designed to be easy to use and integrate with existing projects, making it a great choice for developers who want to build scalable and maintainable applications.',
    link: null,
    projectType: 'Library',
    github: 'https://github.com/nixixjs/nixix',
    videoUrl: 'https://example.com/video1.mp4'
  },
  {
    title: 'Easy Milestones',
    tags: ['tag3', 'tag4'],
    description: 'Trustless smart contract that allows you to create time-based milestone payments',
    // generate long text explaining the project
    deep_dive: 'Easy Milestones is a trustless smart contract that allows you to create time-based milestone payments. It provides a simple and intuitive API for creating and managing milestone payments. \n\nThe contract is designed to be easy to use and integrate with existing projects, making it a great choice for developers who want to build scalable and maintainable applications.',
    link: null,
    projectType: 'SaaS',
    github: 'https://github.com/michTheBrandofficial/easymilestones/',
    type: 'image',
    imageUrl: 'https://example.com/image2.jpg'
  }
];
