"use client";
import * as React from "react";
import { useState } from "react";
import { AnimatePresence, motion } from "motion/react";
import { LinkCircleIcon } from "@hugeicons/core-free-icons";
import { HugeiconsIcon } from "@hugeicons/react";
import <PERSON>ript from "next/script";
import { Twitter } from "./icons/social-media";
import { useModalsBuilder } from "@/lib/use-modals-builder";
import { cn } from "@/lib/utils";

interface NavButtonsProps {}

const NavButtons: React.FC<NavButtonsProps> = ({}) => {
  const { modals, modalFunctions } = useModalsBuilder({
    share: {
      open: false,
    },
    menu: {
      open: false,
    },
  });
  return (
    <>
      <div
        className={cn("absolute top-8 left-8 z-40", {
          "z-50": modals.share.open,
        })}
      >
        <MenuButton
          open={modals.share.open}
          toggle={() => {
            modals.share.open
              ? modalFunctions.closeModal("share")
              : modalFunctions.openModal("share", {});
          }}
        />
      </div>
      {false && (
        <div className="absolute top-8 right-8 z-40">
          <motion.button
            whileTap={{ scale: 0.9 }}
            whileHover={{ scale: 1.1 }}
            onTap={() => modalFunctions.openModal("share", {})}
            className="text-[#111827] text-sm font-medium tracking-wider transition-colors"
          >
            <HugeiconsIcon icon={LinkCircleIcon} className="text-[inherit]" />
          </motion.button>
        </div>
      )}
      <AnimatePresence mode="wait">
        {modals.share.open && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            key={"share"}
            className="fixed top-0 left-0 w-full h-screen inset-0 bg-gray-100/95 backdrop-blur-sm z-40 flex items-center justify-center"
          >
            <div className="max-w-2xl w-full h-full px-8 py-6 overflow-y-auto no-scrollbar">
              <div className="space-y-12 pt-20">
                {/* Menu Items */}
                <div className="space-y-8">
                  <div className="pb-6 flex flex-col gap">
                    <div className="flex gap-x-4">
                      <a href="https://x.com/mich_thedev" target="_blank">
                        <h3 className="text-xl font-medium text-gray-800 tracking-wide mb-2">
                          <Twitter className="size-[22px] " />
                        </h3>
                      </a>
                      <a
                        href="https://github.com/michthebrandofficial"
                        target="_blank"
                        className="inline-block -mt-0.5"
                      >
                        <img
                          src={
                            "https://banner2.cleanpng.com/20180502/xgw/kisspng-github-computer-icons-directory-ming-hua-dado-5aea7d1bc225d6.5661248715253168917952.jpg"
                          }
                          className="size-[26px] bg-transparent "
                        />
                      </a>
                      <div
                        className="github-profile-badge"
                        data-user="michthebrandofficial"
                      ></div>
                    </div>
                    <blockquote className="twitter-tweet">
                      <p lang="en" dir="ltr">
                        GM X community! I&#39;m a specialized Ethereum dApp
                        developer creating custom Web3 solutions that deliver
                        real value.
                        <br />
                        <br />
                        Have a project in mind? DM me to discuss how we can
                        bring your blockchain vision to life.
                        <br />
                        <br />
                        Check out some of my recent work below 👇{" "}
                        <a href="https://t.co/vSSe1VldzP">
                          pic.twitter.com/vSSe1VldzP
                        </a>
                      </p>
                      &mdash; michthebrand (@mich_thedev){" "}
                      <a href="https://twitter.com/mich_thedev/status/1919621899405193266?ref_src=twsrc%5Etfw">
                        May 6, 2025
                      </a>
                    </blockquote>
                    <Script
                      async
                      src="https://platform.twitter.com/widgets.js"
                      // @ts-ignore
                      charSet="utf-8"
                    ></Script>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <Script src="https://cdn.jsdelivr.net/gh/Rapsssito/github-profile-badge@latest/src/widget.min.js"></Script>
    </>
  );
};

const MenuButton = (props: { open: boolean; toggle: VoidFunction }) => {
  return (
    <motion.button
      type="button"
      onTap={() => {
        props.toggle();
      }}
      className="flex h-fit flex-col gap-y-2.5 border-none focus:outline-none"
    >
      <motion.div
        variants={{
          closed: {
            y: "0",
            rotate: "0deg",
          },
          open: {
            y: "310%",
            rotate: "45deg",
          },
        }}
        initial={"closed"}
        animate={props.open ? "open" : "closed"}
        transition={{
          type: "keyframes",
        }}
        className="w-7 h-fit border-b-2 border-[#111827]"
      ></motion.div>
      <motion.div
        variants={{
          closed: {
            y: "0",
            rotate: "0deg",
          },
          open: {
            y: "-310%",
            rotate: "-45deg",
          },
        }}
        initial={"closed"}
        animate={props.open ? "open" : "closed"}
        transition={{
          type: "keyframes",
        }}
        className="w-7 h-fit border-b-2 border-[#111827]"
      ></motion.div>
    </motion.button>
  );
};

export default NavButtons;
