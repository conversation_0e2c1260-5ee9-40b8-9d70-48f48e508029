"use client";
import { motion, useScroll, useTransform } from "motion/react";
import { useEffect, useState } from "react";
import { HugeiconsIcon } from "@hugeicons/react";
import { ArrowUp02FreeIcons } from "@hugeicons/core-free-icons";

/**
 * @dev this has the parallax effect, mount one first
 */
const PuzzlePieceProject: React.FC = (_props) => {
  const [_window, set_Window] = useState(null as Window | null);
  useEffect(() => {
    set_Window(window);
  }, []);
  const { innerHeight = 0 } = _window || {};
  const { scrollY } = useScroll();
  const skewX = useTransform(scrollY, [0, innerHeight], ["-12deg", "0deg"]);
  const skewY = useTransform(scrollY, [0, innerHeight], ["6deg", "0deg"]);
  const [width, height] = [
    useTransform(scrollY, [0, innerHeight], ["12rem", "100vw"]),
    useTransform(scrollY, [0, innerHeight], ["15rem", "100vh"]),
  ];

  return (
    <motion.div
      style={{
        skewX,
        skewY,
        width,
        height,
      }}
      className="fixed inset-0 z-50 bg-white translate-x-1/2 translate-y-1/2"
    >
      {
        <div className="w-full h-full pt-32 pb-20 font-Neue_Montreal overflow-y-auto thin-scrollbar">
          <div className="flex mx-auto max-w-screen-lg items-end justify-between flex-wrap gap-y-3 px-6 font-Neue_Montreal text-[#121f3e] ">
            <h1 className="text-4xl font-bold text-center md:text-6xl">
              Puzzle Piece - (
              <span className="hidden md:inline-block">B2B SaaS - </span>
              2024)
            </h1>
            <p className="">(Scroll)</p>
          </div>
          <div className="flex  justify-between flex-wrap gap-y-3 px-3 max-w-screen-xl mx-auto py-7 md:px-9 md:py-16 md:bg-[#e3e3e3] rounded-2xl md:mt-32 ">
            <img
              width="2432"
              height="1469"
              alt="Image"
              src={
                "https://res.cloudinary.com/dv8vnapx6/image/upload/v1758674069/lappy_image_lhl1wm.png"
              }
            />
          </div>
          <div className="flex flex-col md:flex-row-reverse gap-y-8 px-4 max-w-screen-xl mx-auto py-8 md:py-20 md:mt-10 ">
            <div className="grid grid-cols-2 w-full md:w-[30%] gap-y-6 gap-x-3">
              <div className="flex flex-col gap-y-2">
                <p className="text-stone-400 text-sm ">Customer</p>
                <p className="text-[#121f3e] ">Puzzle Piece</p>
              </div>
              <div className="flex flex-col gap-y-2">
                <p className="text-stone-400 text-sm ">Project Type</p>
                <p className="text-[#121f3e] ">B2B SaaS</p>
              </div>
              <div className="flex flex-col gap-y-2">
                <p className="text-stone-400 text-sm ">Year</p>
                <p className="text-[#121f3e] ">(2024)</p>
              </div>
            </div>
            <div className="flex flex-col gap-y-5 md:w-[70%] ">
              <p className="text-xl">
                Puzzle Piece is a Growth Marketing Tool. You're building
                something important — and you shouldn't have to do it alone.
              </p>
              <p className="text-base flex items-end gap-x-1">
                <a
                  className="underline text-[#171717] "
                  href="https://puzzlepiecesolutions.com"
                >
                  Visit live version
                </a>
                <HugeiconsIcon
                  icon={ArrowUp02FreeIcons}
                  className="rotate-45 "
                />
              </p>
            </div>
          </div>
          <div className="flex flex-col gap-y-5 mx-auto md:w-[70%] ">
            <img
              width="1582"
              height="3962"
              alt="Image"
              src={
                "https://res.cloudinary.com/dv8vnapx6/image/upload/v1758674107/lappy_image_two_ls5qln.png"
              }
            />
          </div>
          <div className="w-full mx-auto max-w-screen-xl px-4 md:px-8 pt-10 md:pt-20 flex flex-col gap-y-20">
            <div className="w-full space-y-3">
              <div className="w-full border-b border-stone-400 " />
              <p className="flex items-center gap-x-1 text-stone-400 ">
                <HugeiconsIcon
                  icon={ArrowUp02FreeIcons}
                  className="rotate-[135deg] "
                />{" "}
                (Next Project)
              </p>
            </div>
            <h1 className="text-4xl font-bold text-center md:text-6xl">
              What's Next?{" "}
              <span className="text-lg text-stone-500">
                Keep <br /> Scrolling to find out
              </span>
            </h1>
          </div>
        </div>
      }
    </motion.div>
  );
};

export default PuzzlePieceProject;
